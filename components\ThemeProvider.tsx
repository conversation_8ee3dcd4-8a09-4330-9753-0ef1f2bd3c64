"use client";

import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes/dist/types";
import { useEffect } from "react";
import useThemeStore from "@/store/useTheme";

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  const { theme, setTheme, setIsDarkMode } = useThemeStore();

  useEffect(() => {
    // Sync with system preference changes
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    
    const handleChange = () => {
      if (theme === "system") {
        setIsDarkMode(mediaQuery.matches);
      }
    };

    // Set initial dark mode state
    if (theme === "system") {
      setIsDarkMode(mediaQuery.matches);
    } else {
      setIsDarkMode(theme === "dark");
    }

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [theme, setIsDarkMode]);

  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      {...props}
    >
      {children}
    </NextThemesProvider>
  );
}
