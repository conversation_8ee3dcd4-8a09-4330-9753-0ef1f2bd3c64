"use client";

import React, { useState, useEffect } from "react";
import { useTheme } from "next-themes";

import { ThemeToggle } from "@/components/ThemeToggle";
import WorkspaceSidebar from "@/components/WorkspaceSidebar";

export default function WorkspacePage() {
  const { theme } = useTheme();
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, []);

  // Don't render theme-dependent content until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden flex">
      {/* Theme toggle button */}
      <ThemeToggle />

      {/* Minimal base background */}
      <div className="absolute inset-0 bg-gray-50 dark:bg-black" />

      {/* Dark mode enhancement layer */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-gray-900/5 dark:from-purple-950/20 dark:via-blue-950/10 dark:to-gray-950/30" />

      {/* Subtle texture for dark mode */}
      <div
        className="absolute inset-0 opacity-0 dark:opacity-[0.02] pointer-events-none"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
        }}
      />

      {/* Grid lines with 0.3 opacity */}
      <div
        className="absolute inset-0 opacity-10 transition-opacity duration-300"
        style={{
          backgroundImage: `
            linear-gradient(to right, rgba(139, 92, 246, 0.3) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(139, 92, 246, 0.3) 1px, transparent 1px)
          `,
          backgroundSize: "40px 40px",
        }}
      />

      {/* Subtle circular gradients for depth */}
      <div
        className="absolute top-0 left-1/4 w-96 h-96 rounded-full blur-3xl opacity-60 dark:opacity-40"
        style={{
          background:
            theme === "dark"
              ? "radial-gradient(circle, rgba(139, 92, 246, 0.15) 0%, transparent 70%)"
              : "radial-gradient(circle, rgba(139, 92, 246, 0.08) 0%, transparent 70%)",
        }}
      />
      <div
        className="absolute bottom-0 right-1/4 w-80 h-80 rounded-full blur-3xl opacity-60 dark:opacity-40"
        style={{
          background:
            theme === "dark"
              ? "radial-gradient(circle, rgba(59, 130, 246, 0.12) 0%, transparent 70%)"
              : "radial-gradient(circle, rgba(59, 130, 246, 0.06) 0%, transparent 70%)",
        }}
      />
      <div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full blur-2xl opacity-50 dark:opacity-30"
        style={{
          background:
            theme === "dark"
              ? "radial-gradient(circle, rgba(6, 182, 212, 0.1) 0%, transparent 70%)"
              : "radial-gradient(circle, rgba(6, 182, 212, 0.05) 0%, transparent 70%)",
        }}
      />

      {/* Subtle circular reveal effect */}
      <div
        className="absolute inset-0 pointer-events-none opacity-60 dark:opacity-80"
        style={{
          background:
            theme === "dark"
              ? `radial-gradient(circle 300px at ${mousePos.x}px ${mousePos.y}px,
                rgba(139, 92, 246, 0.08) 0%,
                rgba(59, 130, 246, 0.04) 40%,
                transparent 70%)`
              : `radial-gradient(circle 300px at ${mousePos.x}px ${mousePos.y}px,
                rgba(139, 92, 246, 0.04) 0%,
                rgba(59, 130, 246, 0.02) 40%,
                transparent 70%)`,
        }}
      />

      {/* Sidebar */}
      <div className="relative z-10">
        <WorkspaceSidebar />
      </div>

      {/* Main Content */}
      <div className="flex-1 relative z-10">
        <main className="p-8">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
              Dashboard
            </h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Dashboard content will go here */}
              <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl p-6 rounded-lg border border-gray-200/50 dark:border-gray-700/50 shadow-lg dark:shadow-purple-500/10">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Welcome to Dashboard
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Your workspace is ready. Start building amazing things!
                </p>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
