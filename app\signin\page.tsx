"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Code, Mail, Github } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import useAuthStore from "@/store/useAuth";
import { Separator } from "@/components/ui/separator";
import { useAuthActions } from "@convex-dev/auth/react";
import { useTheme } from "next-themes";
import { ThemeToggle } from "@/components/ThemeToggle";

const page = () => {
  const { email, setEmail, authLoading, setAuthLoading, message, setMessage } =
    useAuthStore();
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const { theme } = useTheme();
  const { signIn } = useAuthActions();
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleMagicLink = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    setAuthLoading(true);

    try {
      signIn("resend", { email });
      setMessage("Check your email for a sign-in link!");
    } catch (err) {
      setMessage("Failed to send magic link. Please try again.");
    } finally {
      setAuthLoading(false);
    }
  };



  // Don't render theme-dependent content until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden flex items-center justify-center p-4">
      {/* Theme toggle button */}
      <ThemeToggle />

      {/* AI-inspired gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-violet-50 via-blue-50 to-cyan-50 dark:from-slate-900 dark:via-purple-900/20 dark:to-blue-900/20" />

      {/* AI accent gradients */}
      <div className="absolute inset-0 bg-gradient-to-tr from-purple-500/10 via-transparent to-cyan-500/10 dark:from-purple-500/20 dark:via-transparent dark:to-cyan-500/20" />
      <div className="absolute inset-0 bg-gradient-to-bl from-blue-500/5 via-transparent to-violet-500/5 dark:from-blue-500/15 dark:via-transparent dark:to-violet-500/15" />

      {/* Floating AI orbs */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-purple-400/20 to-blue-400/20 rounded-full blur-xl animate-pulse" />
      <div className="absolute bottom-32 right-16 w-24 h-24 bg-gradient-to-r from-cyan-400/20 to-violet-400/20 rounded-full blur-lg animate-pulse delay-1000" />
      <div className="absolute top-1/2 left-8 w-16 h-16 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-md animate-pulse delay-500" />
      
      {/* AI grid pattern background */}
      <div
        className="absolute inset-0 opacity-30 dark:opacity-20 transition-opacity duration-300"
        style={{
          backgroundImage: `
            linear-gradient(to right, rgba(139, 92, 246, 0.1) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(139, 92, 246, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px'
        }}
      />
      
      {/* AI-enhanced circular reveal mask */}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{
          background: theme === "dark"
            ? `radial-gradient(circle 200px at ${mousePos.x}px ${mousePos.y}px,
                rgba(139, 92, 246, 0.1) 0%,
                rgba(59, 130, 246, 0.05) 30%,
                transparent 50%,
                rgba(15, 23, 42, 0.9) 80%)`
            : `radial-gradient(circle 200px at ${mousePos.x}px ${mousePos.y}px,
                rgba(139, 92, 246, 0.15) 0%,
                rgba(59, 130, 246, 0.1) 30%,
                transparent 50%,
                rgba(255, 255, 255, 0.8) 80%)`,
          maskImage: `
            linear-gradient(to right, ${theme === "dark" ? "rgba(139, 92, 246, 0.3)" : "rgba(139, 92, 246, 0.4)"} 1px, transparent 1px),
            linear-gradient(to bottom, ${theme === "dark" ? "rgba(139, 92, 246, 0.3)" : "rgba(139, 92, 246, 0.4)"} 1px, transparent 1px)
          `,
          maskSize: '40px 40px',
          WebkitMaskImage: `
            linear-gradient(to right, ${theme === "dark" ? "rgba(139, 92, 246, 0.3)" : "rgba(139, 92, 246, 0.4)"} 1px, transparent 1px),
            linear-gradient(to bottom, ${theme === "dark" ? "rgba(139, 92, 246, 0.3)" : "rgba(139, 92, 246, 0.4)"} 1px, transparent 1px)
          `,
          WebkitMaskSize: '40px 40px'
        }}
      />

      <Card className="w-full max-w-md border border-purple-200/20 dark:border-purple-500/20 shadow-2xl bg-white/90 backdrop-blur-md dark:bg-slate-900/90 relative z-10 ring-1 ring-purple-500/10 dark:ring-purple-400/20">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 rounded-lg flex items-center justify-center shadow-lg shadow-purple-500/25">
              <Code className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-slate-900 dark:text-white">
              Reflow
            </span>
          </div>
          <CardTitle className="text-2xl">Welcome back</CardTitle>
          <CardDescription>
            Sign in to your account to continue building dynamic UIs
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <form onSubmit={handleMagicLink} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setEmail(e.target.value)
                  }
                  className="pl-10"
                  required
                />
              </div>
            </div>

            <Button
              type="submit"
              className="w-full cursor-pointer"
              disabled={authLoading}
            >
              {authLoading ? "Loading..." : "Sign in/Sign up"}
            </Button>
          </form>

          {message && (
            <div className="text-sm text-center p-3 rounded-md bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800">
              {message}
            </div>
          )}

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white dark:bg-slate-800 px-2 text-slate-500">
                Or continue with
              </span>
            </div>
          </div>

          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full cursor-pointer"
              onClick={() =>
                void signIn("google", { redirectTo: "http://localhost:3000/" })
              }
            >
              <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                <path
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  fill="#4285F4"
                />
                <path
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  fill="#34A853"
                />
                <path
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  fill="#FBBC05"
                />
                <path
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  fill="#EA4335"
                />
              </svg>
              Continue with Google
            </Button>

            <Button
              variant="outline"
              className="w-full cursor-pointer"
              onClick={() =>
                void signIn("github", {
                  redirectTo: "http://localhost:3000/",
                })
              }
            >
              <Github className="mr-2 h-4 w-4" />
              Continue with GitHub
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default page;

