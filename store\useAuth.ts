import { create } from "zustand";

interface AuthStore {
    email: string;
    setEmail: (email: string) => void;
    authLoading: boolean;
    setAuthLoading: (loading: boolean) => void;
    message: string;
    setMessage: (message: string) => void;
}

const useAuthStore = create<AuthStore>((set) => ({
    email: "",
    setEmail: (email) => set({ email }),
    authLoading: false,
    setAuthLoading: (loading) => set({ authLoading: loading }),
    message: "",
    setMessage: (message) => set({ message }),
}));

export default useAuthStore;