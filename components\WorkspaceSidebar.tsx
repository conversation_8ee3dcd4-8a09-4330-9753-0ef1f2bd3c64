"use client";

import React, { useState } from "react";
import {
  Home,
  Wallet,
  Bell,
  Award,
  FileText,
  Heart,
  BarChart3,
  Users,
  Settings,
  HelpCircle,
  MessageSquare,
  ChevronLeft,
  ChevronRight,
  LayoutDashboard,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Dummy data for menu items
const menuItems = [
  {
    icon: Home,
    label: "Dashboard",
    href: "/workspace",
    isActive: true,
  },
  {
    icon: Wallet,
    label: "My wallet",
    href: "/workspace/wallet",
  },
  {
    icon: Bell,
    label: "Notifications",
    href: "/workspace/notifications",
    badge: "12",
  },
  {
    icon: Award,
    label: "Badge's",
    href: "/workspace/badges",
  },
  {
    icon: FileText,
    label: "My reports",
    href: "/workspace/reports",
    isActive: true,
  },
  {
    icon: Heart,
    label: "Favorit List",
    href: "/workspace/favorites",
  },
  {
    icon: BarChart3,
    label: "Analytics",
    href: "/workspace/analytics",
  },
  {
    icon: Users,
    label: "Team",
    href: "/workspace/team",
  },
];

// Dummy user data
const userData = {
  name: "M.Sohaib Ali",
  email: "<EMAIL>",
  avatar: "/api/placeholder/40/40",
  initials: "MS",
};

interface MenuItemProps {
  icon: React.ElementType;
  label: string;
  href: string;
  isActive?: boolean;
  badge?: string;
  isCollapsed: boolean;
}

function MenuItem({ icon: Icon, label, href, isActive = false, badge, isCollapsed }: MenuItemProps) {
  return (
    <li>
      <a
        href={href}
        className={cn(
          "flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 group relative",
          isActive
            ? "bg-slate-700 dark:bg-slate-600 text-white"
            : "text-slate-300 hover:text-white hover:bg-slate-700/50 dark:hover:bg-slate-600/50"
        )}
        title={isCollapsed ? label : undefined}
      >
        <div className="flex items-center space-x-3">
          <Icon className="w-4 h-4 flex-shrink-0" />
          {!isCollapsed && <span className="truncate">{label}</span>}
        </div>
        {badge && !isCollapsed && (
          <span className="bg-blue-500 text-white text-xs px-2 py-0.5 rounded-full min-w-[20px] text-center flex-shrink-0">
            {badge}
          </span>
        )}
        {badge && isCollapsed && (
          <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">
            {badge}
          </span>
        )}
      </a>
    </li>
  );
}

function WorkspaceSidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div
      className={cn(
        "h-screen bg-slate-800 dark:bg-slate-900 flex flex-col transition-all duration-300 ease-in-out relative",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      {/* Header */}
      <div className="p-4 border-b border-slate-700 dark:border-slate-600">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-slate-600 dark:bg-slate-700 rounded-lg flex items-center justify-center flex-shrink-0">
              <LayoutDashboard className="w-4 h-4 text-white" />
            </div>
            {!isCollapsed && (
              <span className="text-white font-medium text-lg truncate">Dashboard</span>
            )}
          </div>
        </div>
      </div>

      {/* Toggle Button */}
      <Button
        onClick={toggleSidebar}
        variant="ghost"
        size="sm"
        className="absolute -right-3 top-20 z-10 bg-slate-700 hover:bg-slate-600 text-white border border-slate-600 rounded-full w-6 h-6 p-0 shadow-lg"
      >
        {isCollapsed ? (
          <ChevronRight className="w-3 h-3" />
        ) : (
          <ChevronLeft className="w-3 h-3" />
        )}
      </Button>

      {/* Navigation */}
      <nav className="flex-1 py-4 overflow-y-auto">
        <ul className="space-y-1 px-3">
          {menuItems.map((item, index) => (
            <MenuItem
              key={index}
              icon={item.icon}
              label={item.label}
              href={item.href}
              isActive={item.isActive}
              badge={item.badge}
              isCollapsed={isCollapsed}
            />
          ))}
        </ul>
      </nav>

      {/* Footer */}
      <div className="border-t border-slate-700 dark:border-slate-600">
        {/* Settings and Help Icons */}
        <div className={cn("flex py-4", isCollapsed ? "flex-col space-y-2 items-center" : "justify-center space-x-4")}>
          <button
            className="p-2 text-slate-400 hover:text-white hover:bg-slate-700 dark:hover:bg-slate-600 rounded-lg transition-colors duration-200"
            title="Help"
          >
            <HelpCircle className="w-5 h-5" />
          </button>
          <button
            className="p-2 text-slate-400 hover:text-white hover:bg-slate-700 dark:hover:bg-slate-600 rounded-lg transition-colors duration-200"
            title="Settings"
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>

        {/* User Profile */}
        <div className="p-4 border-t border-slate-700 dark:border-slate-600">
          <div className={cn("flex items-center", isCollapsed ? "justify-center" : "space-x-3")}>
            <Avatar className="w-10 h-10 flex-shrink-0">
              <AvatarImage src={userData.avatar} alt={userData.name} />
              <AvatarFallback className="bg-slate-600 text-white text-sm">
                {userData.initials}
              </AvatarFallback>
            </Avatar>
            {!isCollapsed && (
              <>
                <div className="flex-1 min-w-0">
                  <p className="text-white text-sm font-medium truncate">
                    {userData.name}
                  </p>
                  <p className="text-slate-400 text-xs truncate">
                    {userData.email}
                  </p>
                </div>
                <button className="text-slate-400 hover:text-white transition-colors duration-200 flex-shrink-0">
                  <MessageSquare className="w-4 h-4" />
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default WorkspaceSidebar;
